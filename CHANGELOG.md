# Changelog
## v21.6.0-beta
- feat: update to MC 1.21.6

## v21.5.1
- fix: don't hard-crash if a config file can't be read

## v21.5.0
- Update to MC 1.21.5.

## v21.4.2
- Update to MC 1.21.4.

## v21.3.0
- Update to MC 1.21.3.

## v21.2.0
- Update to MC 1.21.2.

## v21.1.0
- Update to MC 1.21.1.

## v21.0.4
- Fix race condition on NeoForge when registering KeyMappings.

## v21.0.3
- Update build scripts using [MDK Generator](https://github.com/Trikzon/mdk-generator). No functional changes.

## v21.0.2
### v21.0.2-beta
- Fix ModOptionsScreen crash caused by NeoForge breaking change

### v21.0.1-beta
- Fix ModOptionsScreen rendering duplicate option buttons

### v21.0.0-beta
- Update to MC 1.21
- Remove Forge support
- Remove NeoForgeModLoader. Mods no longer need to register themselves with
  Ash API on NeoForge
- Remove empty NeoForge entrypoint. NeoForge does not require one anymore
- Remove Ash ModInitializers completely
  1. Mods no longer have to register themselves with Ash API on NeoForge
  2. NeoForge now allows for client-only entry points
- Rename ClientTickEvents to ClientTickEvent
  - Rename start and end callbacks to pre and post to match NeoForge
- Rename `ModOptionsScreenRegistry#registerModOptionsScreen` to just `ModOptionsScreenRegistry#register`
- Rename `KeyMappingRegistry#registerKeyMapping` to just `KeyMappingRegistry#register`
- Rewrite UseBlockEvent to use root level functional interface

## v20.6.2
### v20.6.2-beta
- Fix jar file for Forge build. Forge no longer re-obfuscates its jars.

### v20.6.1-beta
- Fix ModOptionsScreen on 1.20.6.

### v20.6.0-beta
- Updated to MC 1.20.6

## v20.4.0
- Updated to MC 1.20.4.
- Fixed logo file on NeoForge.

## v20.2.3
### v20.2.3-beta
- Fixed crash when opening the mod menu entry on NeoForge

### 20.2.2-beta
- Fixed ModOptionsScreen registration on Forge and NeoForge

### 20.2.1-beta
- Fixed links in modloader meta files.
- Fixed ModOptionsScreen rendering. Likely broke due to 1.20.2.

### 20.2.0-beta
- Changed version format to follow NeoForge's `<minecraft_minor>.<minecraft_patch>.<number>(-beta)`.
  - Read more about it [here](https://neoforged.net/news/20.2release/).
- No longer directly provide Quilt mod loader support. The Fabric version will likely continue to work on Quilt.
- Add support for the NeoForge mod loader.
- Heavily rearranged packages, including a change to the `ash_api` package.
- Made classes and methods use appropriate jetbrains annotations.

## 3.0.2+1.20.1
- Fix version in jar file

## 3.0.1+1.20.1
- Add `*ModInitializer` APIs to give Ash control over initialization flow.

## 3.0.0+1.20.1
- Update to MC 1.20.1

## 2.1.0+1.20
- Change mod id to `ash_api`
- Add minecraft version to the end of version number

## 2.0.0
### 2.0.0-rc.2
- Fix ModOptionsScreenRegistry on Quilt

### 2.0.0-rc.1
- Port to 1.20

## 1.0.1
- Fix crash when Mod Menu is not installed but a `ModOptionsScreen` is registered.

## 1.0.0
- Add `ForgeModLoader` API on Forge
- Add `ModOptions` and `ModOptionsManager` API
- Add `ModOptionsScreen` and `ModOptionsScreenRegistry` API
- Add `ResourceLoader` API to allow registering resource reload listeners.
  - Note: It has not been tested for server-side resource reload listeners, but it should work.
- Add `ModLoader` API
- Add `UseBlockEvent` API
- Add `ClientTickEvents` API
- Add `KeyMappingRegistry` API
- Add `ModEventBus` API on Forge
