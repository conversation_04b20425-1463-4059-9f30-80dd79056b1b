name: Build
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v2
      - name: setup jdk 21
        uses: actions/setup-java@v1
        with:
          java-version: "21"
      - name: make gradle wrapper executable
        if: ${{ runner.os != 'Windows' }}
        run: chmod +x ./gradlew
      - name: build
        run: ./gradlew build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-jars
          path: |
            neoforge/build/libs/*.jar
            fabric/build/libs/*.jar
          if-no-files-found: warn
