plugins {
    id 'java-library'
    id 'maven-publish'
}

base {
    archivesName = "${project.mod_id}-${project.name}"
}

java {
    toolchain.languageVersion = JavaLanguageVersion.of(project.java_version)
    withSourcesJar()
    withJavadocJar()
}

repositories {
    mavenCentral()
    exclusiveContent {
        forRepository {
            maven {
                name = 'Sponge'
                url = 'https://repo.spongepowered.org/repository/maven-public'
            }
        }
        filter { includeGroupAndSubgroups('org.spongepowered') }
    }
    exclusiveContent {
        forRepositories(
            maven {
                name = 'ParchmentMC'
                url = 'https://maven.parchmentmc.org/'
            },
            maven {
                name = 'NeoForge'
                url = 'https://maven.neoforged.net/releases'
            }
        )
        filter { includeGroup('org.parchmentmc.data') }
    }
    maven {
        name = 'BlameJared'
        url = 'https://maven.blamejared.com'
    }
    maven {
        name = 'Tryban'
        url = 'https://maven.tryban.dev/releases'
    }
}

['apiElements', 'runtimeElements', 'sourcesElements', 'javadocElements'].each { variant ->
    configurations."$variant".outgoing {
        capability("${project.group}:${project.name}:${project.version}")
        capability("${project.group}:${base.archivesName.get()}:${project.version}")
        capability("${project.group}:${project.mod_id}-${project.name}-${project.minecraft_version}:${project.version}")
        capability("${project.group}:${project.mod_id}:${project.version}")
    }
    publishing.publications.configureEach {
        suppressPomMetadataWarningsFor(variant)
    }
}

sourcesJar {
    from(rootProject.file('LICENSE')) {
        rename { "${it}_${project.mod_name}" }
    }
}

jar {
    from(rootProject.file('LICENSE')) {
        rename { "${it}_${project.mod_name}" }
    }

    manifest {
        attributes([
            'Specification-Title'   : project.mod_name,
            'Specification-Vendor'  : project.mod_author,
            'Specification-Version' : project.jar.archiveVersion,
            'Implementation-Title'  : project.name,
            'Implementation-Version': project.jar.archiveVersion,
            'Implementation-Vendor' : project.mod_author,
            'Built-On-Minecraft'    : project.minecraft_version
        ])
    }
}

processResources {
    var expandProps = [
            'group'                        : project.group,
            'java_version'                 : project.java_version,
            'mod_id'                       : project.mod_id,
            'mod_name'                     : project.mod_name,
            'mod_author'                   : project.mod_author,
            'version'                      : project.version,
            'license'                      : project.license,
            'credits'                      : project.credits,
            'description'                  : project.description,
            'minecraft_version'            : project.minecraft_version,
            'minecraft_version_range'      : project.minecraft_version_range,
            'neoform_version'              : project.neoform_version,
            'parchment_minecraft_version'  : project.parchment_minecraft_version,
            'parchment_version'            : project.parchment_version,
            'ash_version'                  : project.ash_version,
            'fabric_version'               : project.fabric_version,
            'fabric_loader_version'        : project.fabric_loader_version,
            'modmenu_version'              : project.modmenu_version,
            'neoforge_version'             : project.neoforge_version,
            'neoforge_loader_version_range': project.neoforge_loader_version_range,
    ]

    var jsonExpandProps = expandProps.collectEntries {
        key, value -> [(key): value instanceof String ? value.replace('\n', '\\\\n') : value]
    }

    filesMatching(['META-INF/neoforge.mods.toml']) {
        expand expandProps
    }

    filesMatching(['pack.mcmeta', 'fabric.mod.json', '*.mixins.json']) {
        expand jsonExpandProps
    }

    inputs.properties(expandProps)
}

publishing {
    publications {
        register('mavenJava', MavenPublication) {
            artifactId base.archivesName.get()
            from components.java
        }
    }
    repositories {
        if (System.getenv('TRYBAN_MAVEN_USER') && System.getenv('TRYBAN_MAVEN_TOKEN')) {
            maven {
                url = "https://maven.tryban.dev/releases"
                credentials {
                    username = System.getenv('TRYBAN_MAVEN_USER')
                    password = System.getenv('TRYBAN_MAVEN_TOKEN')
                }
            }
        }
    }
}
