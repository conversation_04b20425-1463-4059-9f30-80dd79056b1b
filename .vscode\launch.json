{"version": "0.2.0", "configurations": [{"type": "java", "request": "launch", "name": "NeoForge Client (:neoforge)", "presentation": {"group": "Mod Development - neoforge", "order": 0}, "projectName": "neoforge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\clientRunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\clientRunVmArgs.txt", "-Dfml.modFolders=ash_api%%B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\bin\\main"], "cwd": "${workspaceFolder}\\neoforge\\run", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "NeoForge Data (:neoforge)", "presentation": {"group": "Mod Development - neoforge", "order": 1}, "projectName": "neoforge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\dataRunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\dataRunVmArgs.txt", "-Dfml.modFolders=ash_api%%B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\bin\\main"], "cwd": "${workspaceFolder}\\neoforge\\run", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "NeoForge Server (:neoforge)", "presentation": {"group": "Mod Development - neoforge", "order": 2}, "projectName": "neoforge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\serverRunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\build\\moddev\\serverRunVmArgs.txt", "-Dfml.modFolders=ash_api%%B:\\Google Chrome\\ash-api-1.21.6\\ash-api-1.21.6\\neoforge\\bin\\main"], "cwd": "${workspaceFolder}\\neoforge\\run", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}]}