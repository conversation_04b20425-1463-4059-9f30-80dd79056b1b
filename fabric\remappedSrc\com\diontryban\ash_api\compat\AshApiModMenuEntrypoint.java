/*
 * This file is part of Ash API.
 * A copy of this program can be found at https://github.com/Trikzon/ash-api.
 * Copyright (C) 2023 Dion Tryban
 *
 * Ash API is free software: you can redistribute it and/or modify it under the
 * terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation, either version 3 of the License, or (at your option)
 * any later version.
 *
 * Ash API is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Ash API. If not, see <https://www.gnu.org/licenses/>.
 */

package com.diontryban.ash_api.compat;

import com.terraformersmc.modmenu.api.ConfigScreenFactory;
import com.terraformersmc.modmenu.api.ModMenuApi;
import org.jetbrains.annotations.ApiStatus;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import net.minecraft.client.gui.screen.Screen;

@ApiStatus.Internal
public final class AshApiModMenuEntrypoint implements ModMenuApi {
    private static final Map<String, ConfigScreenFactory<?>> CONFIG_SCREEN_FACTORIES = new HashMap<>();

    @Override
    public Map<String, ConfigScreenFactory<?>> getProvidedConfigScreenFactories() {
        return CONFIG_SCREEN_FACTORIES;
    }

    public static void addConfigScreenFactory(
            @NotNull String modId,
            @NotNull Function<Screen, ? extends Screen> factory
    ) {
        CONFIG_SCREEN_FACTORIES.put(modId, factory::apply);
    }
}
