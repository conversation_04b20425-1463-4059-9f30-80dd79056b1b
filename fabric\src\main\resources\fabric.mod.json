{"schemaVersion": 1, "id": "${mod_id}", "version": "${version}", "name": "${mod_name}", "description": "${description}", "authors": ["${mod_author}"], "contact": {"homepage": "https://modrinth.com/mod/ash-api", "sources": "https://github.com/Trikzon/ash-api", "issues": "https://github.com/Trikzon/ash-api/issues"}, "license": "${license}", "icon": "${mod_id}.png", "environment": "*", "entrypoints": {"modmenu": ["com.diontryban.ash_api.compat.AshApiModMenuEntrypoint"]}, "mixins": ["${mod_id}.mixins.json", "${mod_id}.fabric.mixins.json"], "depends": {"fabric-api": "*", "minecraft": "^${minecraft_version}", "java": ">=${java_version}"}, "suggests": {"modmenu": "*"}, "custom": {"modmenu": {"badges": ["library"]}}}