pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
        exclusiveContent {
            forRepository {
                maven {
                    name = 'Fabric'
                    url = uri('https://maven.fabricmc.net')
                }
            }
            filter {
                includeGroup('net.fabricmc')
                includeGroup('net.fabricmc.unpick')
                includeGroup('fabric-loom')
            }
        }
    }
}

plugins {
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.8.0'
}

rootProject.name = 'ash-api'
include('common')
include('fabric')
include('neoforge')
