plugins {
    id 'multiloader-loader'
    id 'fabric-loom'
}

repositories {
    maven {
        name = 'TerraformersMC'
        url = 'https://maven.terraformersmc.com/releases'
    }
}

dependencies {
    minecraft "com.mojang:minecraft:${project.minecraft_version}"

    mappings loom.layered {
        officialMojangMappings()
        parchment("org.parchmentmc.data:parchment-${project.parchment_minecraft_version}:${project.parchment_version}@zip")
    }

    modImplementation "net.fabricmc:fabric-loader:${project.fabric_loader_version}"
    modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"

    if (project.ash_version != "") {
        modImplementation "com.diontryban.ash_api:ash_api-fabric:${project.ash_version}"
    }

    if (project.modmenu_version != "") {
        modApi "com.terraformersmc:modmenu:${project.modmenu_version}"
    }


}

loom {
    def aw = project(':common').file("src/main/resources/${project.mod_id}.accesswidener")
    if (aw.exists()) {
        accessWidenerPath.set(aw)
    }

    mixin {
        defaultRefmapName.set("${project.mod_id}.refmap.json")
    }

    runs {
        client {
            client()
            setConfigName('Fabric Client')
            ideConfigGenerated(true)
            runDir('runs/client')
        }

        server {
            server()
            setConfigName('Fabric Server')
            ideConfigGenerated(true)
            runDir('runs/server')
        }
    }
}
