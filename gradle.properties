# IMPORTANT:
# Every field listed here must be added to the expandProps map in
# buildSrc/src/main/goovy/multiloader-common.gradle.

# Project
group=com.diontryban.ash_api
java_version=21

# Mod
mod_id=ash_api
mod_name=Ash API
mod_author=Trikzon
version=21.8.0-beta
license=LGPL-3.0-or-later
credits=
description=An API that provides an abstraction layer over Fabric and NeoForge APIs.

# Common
minecraft_version=1.21.8
minecraft_version_range=[1.21.8, 1.22)
neoform_version=1.21.6-20250617.151856
parchment_minecraft_version=1.21.8
parchment_version=2025.09.14
ash_version=

# Fabric
fabric_version=0.133.4+1.21.8
fabric_loader_version=0.17.2
modmenu_version=15.0.0-beta.3

# NeoForge
neoforge_version=21.8.47
neoforge_loader_version_range=[4,)

# Gradle
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false
