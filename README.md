# Ash API
[![](http://cf.way2muchnoise.eu/full_838498_downloads.svg)](https://www.curseforge.com/minecraft/mc-mods/ash-api)
[![](https://img.shields.io/modrinth/dt/ash-api?logo=modrinth&style=flat)](https://www.modrinth.com/mod/ash-api)
[![](http://cf.way2muchnoise.eu/versions/838498.svg)](https://www.curseforge.com/minecraft/mc-mods/ash-api)
[![](https://maven.diontryban.com/api/badge/latest/releases/com/diontryban/ash_api/ash_api-common?color=40c14a&name=Maven&prefix=v)](https://maven.diontryban.com/#/releases/com/diontryban/ash_api)

An API that provides an abstraction layer over the APIs of the main Minecraft mod loaders: NeoForge and Fabric. It is meant to be used with a multi-loader development environment such as <PERSON>'s [MultiLoader-Template](https://github.com/jaredlll08/MultiLoader-Template).

Documentation will be available in the near future.

Pull requests are highly appreciated. However, please discuss them in my [Discord](https://discord.gg/aUwZKagWh2) first.

---

Report any issues on [GitHub](https://github.com/Trikzon/ash-api/issues). Chat on [Discord](https://discord.gg/aUwZKagWh2) in the `#mc-mods` channel.

Support the development of my mods on [Ko-fi](https://ko-fi.com/X7X8D56YI).

[![ko-fi](https://ko-fi.com/img/githubbutton_sm.svg)](https://ko-fi.com/X7X8D56YI)
