plugins {
    id 'multiloader-loader'
    id 'net.neoforged.moddev'
}

neoForge {
    version = project.neoforge_version

    validateAccessTransformers = true

    def at = project(':common').file('src/main/resources/META-INF/accesstransformer.cfg')
    if (at.exists()) {
        accessTransformers.from(at.absolutePath)
    }

    parchment {
        minecraftVersion = project.parchment_minecraft_version
        mappingsVersion = project.parchment_version
    }

    runs {
        configureEach {
            systemProperty 'neoforge.enabledGameTestNamespaces', project.mod_id
            // Unify the run config names with fabric.
            ideName = "NeoForge ${it.name.capitalize()} (${project.path})"
        }

        client {
            client()
        }

        data {
            clientData()
        }

        server {
            server()
        }
    }

    mods {
        "${project.mod_id}" {
            sourceSet(sourceSets.main)
        }
    }
}

dependencies {
    if (project.ash_version != "") {
        implementation "com.diontryban.ash_api:ash_api-neoforge:${project.ash_version}"
    }


}

sourceSets.main.resources { srcDir 'src/generated/resources' }
